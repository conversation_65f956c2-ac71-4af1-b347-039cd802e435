from dash import html

##################################################
#---FUNCTIONS FOR DASH / VISUALS ETC---#
def build_weights_table(minvar_weights, maxsharpe_weights, maxsortino_weights, modsharpe_weights, maxret_weights, suggestion_text=None):
    """Create a formatted HTML table of portfolio weights for different optimization strategies"""
    # Helper function to normalize weights (absolute and sum to 1)
    def normalize_weights(weights_dict):
        if not weights_dict:
            return {}
        # Calculate sum of absolute values for normalization
        total_abs = sum(abs(v) for v in weights_dict.values())
        # Normalize original weights (preserving sign) by the sum of absolute values
        if total_abs > 1e-9: # Avoid division by zero or near-zero
            return {k: v / total_abs for k, v in weights_dict.items()}
        # If sum is zero (or very close), return original (likely all zeros) or empty dict
        return weights_dict if weights_dict else {}
    
    # Normalize all weight dictionaries
    norm_minvar = normalize_weights(minvar_weights)
    norm_maxsharpe = normalize_weights(maxsharpe_weights)
    norm_maxsortino = normalize_weights(maxsortino_weights)
    norm_modsharpe = normalize_weights(modsharpe_weights)
    norm_maxret = normalize_weights(maxret_weights)

    # Get all unique symbols across all weight dictionaries
    all_symbols = set()
    for weights in [norm_minvar, norm_maxsharpe, norm_maxsortino, norm_modsharpe, norm_maxret]:
        all_symbols.update(weights.keys())
    
    # Create table header
    table_header = [
        html.Tr([
            html.Th("Symbol", style={'textAlign': 'left'}),
            html.Th("Min Variance", style={'textAlign': 'left'}),
            html.Th("Max Sharpe", style={'textAlign': 'left'}),
            html.Th("Sortino", style={'textAlign': 'left'}),
            html.Th("CF Sharpe", style={'textAlign': 'left'}),
            html.Th("Return", style={'textAlign': 'left'})
        ], style={'color': 'white', 'border': '1px solid white', 'background-color': '#333'})
    ]
    
    # Create table rows
    table_rows = []
    for sym in sorted(all_symbols):
        row = html.Tr([
            # Symbol without sign manipulation
            html.Td(sym, style={'textAlign': 'left'}),
            # Min Variance weight
            html.Td(f'{norm_minvar.get(sym, 0):.2%}'),
            # Max Sharpe weight
            html.Td(f'{norm_maxsharpe.get(sym, 0):.2%}'),
            # Sortino weight
            html.Td(f'{norm_maxsortino.get(sym, 0):.2%}'),
            # Max Modified Sharpe weight
            html.Td(f'{norm_modsharpe.get(sym, 0):.2%}'),
            # Return weight
            html.Td(f'{norm_maxret.get(sym, 0):.2%}')
        ], style={'color': 'white', 'border': '1px solid white'})
        table_rows.append(row)

    # Create the weights table 
    weights_table = html.Table(
        table_header + table_rows,
        style={
            'width': '100%',
            'border': '1px solid white',
            'borderCollapse': 'collapse',
            'marginBottom': '20px'
        }
    )
    
    # Add suggestion text if provided
    if suggestion_text:
        suggestion_div = html.Div(
            html.P(
                [html.B("Suggested additional pairs to reduce collinearity:"), " "] + 
                [html.Span(suggestion_text.replace("<br><b>Suggested additional pairs to reduce collinearity:</b><br>", ""))]
            ),
            style={'color': 'white', 'marginBottom': '10px', 'marginTop': '10px'}
        )
        return html.Div([weights_table, suggestion_div])
    
    return weights_table

def create_combined_mpt_string(original_portfolio, suggested_portfolios, tracker_weights_info=None):
    """
    Create a combined MPT string from original and suggested portfolios with proper weight normalization.
    Each portfolio (original + suggested) gets an equal portion of the total weight.
    
    Args:
        original_portfolio (dict): Dictionary with 'combo' and 'weights' keys
        suggested_portfolios (list): List of dictionaries with 'combo' and 'weights' keys
        
    Returns:
        str: Combined MPT string in three formats (comma-separated weights, TV format, MT format)
    """
    # Filter out invalid suggested portfolios
    valid_suggestions = [p for p in suggested_portfolios if isinstance(p, dict) and 'combo' in p and 'weights' in p]
    
    # Calculate total number of portfolios (original + valid suggestions)
    portfolio_count = len(valid_suggestions)
    if original_portfolio is not None:
        portfolio_count += 1
    
    # Each portfolio gets equal weight
    portfolio_weight = 1.0 / portfolio_count if portfolio_count > 0 else 1.0
    print(f"Normalizing weights across {portfolio_count} portfolios ({portfolio_weight:.2%} each)")
    
    # Start with empty combined weights
    combined_weights = {}
    
    # Process the original portfolio if provided
    if original_portfolio is not None and 'combo' in original_portfolio and 'weights' in original_portfolio:
        try:
            # Get the sum of absolute weights to normalize within the portfolio
            original_total = sum(abs(w) for w in original_portfolio['weights'])
            
            # Add each symbol with proper scaling
            for sym, weight in zip(original_portfolio["combo"], original_portfolio["weights"]):
                # Scale the weight by portfolio's allocation
                scaled_weight = (weight / original_total) * portfolio_weight
                
                # Add to combined weights
                combined_weights[sym] = scaled_weight
        except Exception as e:
            print(f"Error processing original portfolio: {e}")
    
    # Process each suggested portfolio with its allocated weight
    for i, suggestion in enumerate(valid_suggestions):
        try:
            # Get the sum of absolute weights to normalize within the portfolio
            suggestion_total = sum(abs(w) for w in suggestion['weights'])
            
            # Add each symbol with proper scaling
            for sym, weight in zip(suggestion["combo"], suggestion["weights"]):
                # Scale the weight by portfolio's allocation
                scaled_weight = (weight / suggestion_total) * portfolio_weight
                
                # Add to combined weights (accumulate if symbol appears in multiple portfolios)
                if sym in combined_weights:
                    combined_weights[sym] += scaled_weight
                else:
                    combined_weights[sym] = scaled_weight
        except Exception as e:
            print(f"Error processing suggested portfolio {i+1}: {e}")
    
    # Create the standard MPT string from combined weights
    mpt_parts = [f"{sym}:{weight:.4f}" for sym, weight in combined_weights.items() if abs(weight) > 0.0001]
    
    # Create the alternative format string for trading systems (TV) with rounded weights
    tv_parts = []
    
    # First round all weights to 2 decimal places (preserving sign)
    rounded_weights = {sym: round(weight, 2) for sym, weight in combined_weights.items() if abs(weight) > 0.0001}
    
    # Calculate the sum of rounded absolute weights
    rounded_abs_sum = sum(abs(w) for w in rounded_weights.values())
    
    # If the sum is not 1.0, adjust the largest weight
    if abs(rounded_abs_sum - 1.0) > 0.0001 and rounded_weights:
        # Find the symbol with the largest absolute weight
        largest_sym = max(rounded_weights.keys(), key=lambda s: abs(rounded_weights[s]))
        # Adjust this weight to make the total exactly 1.0
        adjustment = 1.0 - (rounded_abs_sum - abs(rounded_weights[largest_sym]))
        # Preserve the sign of the largest weight
        rounded_weights[largest_sym] = adjustment * (1 if rounded_weights[largest_sym] >= 0 else -1)
    
    # Now create the TV parts with adjusted rounded weights
    for sym, weight in rounded_weights.items():
        if weight < 0:
            if "JPY" in sym:
                tv_parts.append(f"{weight:.2f}*(FX:{sym}/100)")
            else:
                tv_parts.append(f"{weight:.2f}*FX:{sym}")
        else:
            if "JPY" in sym:
                tv_parts.append(f"+{weight:.2f}*(FX:{sym}/100)")
            else:
                tv_parts.append(f"+{weight:.2f}*FX:{sym}")
    
    # Create a third format for MetaTrader with uppercase symbols in quotes
    mt_parts = []
    for sym, weight in rounded_weights.items():
        symbol = sym.upper()
        if weight < 0:
            # Handle JPY pairs specially
            if "JPY" in symbol:
                mt_parts.append(f"{weight:.2f}*(\"{symbol}\"/100)")
            else:
                mt_parts.append(f"{weight:.2f}*\"{symbol}\"")
        else:
            # Handle JPY pairs specially
            if "JPY" in symbol:
                mt_parts.append(f"+{weight:.2f}*(\"{symbol}\"/100)")
            else:
                mt_parts.append(f"+{weight:.2f}*\"{symbol}\"")

    # Join parts into strings
    tv_string = "".join(tv_parts)
    mt_string = "".join(mt_parts)
    
    # Remove leading "+" if present
    if tv_string.startswith("+"):
        tv_string = tv_string[1:]
    if mt_string.startswith("+"):
        mt_string = mt_string[1:]

    # Combine all formats with newlines, including tracker weights if provided
    result = ",".join(mpt_parts) + "\n" + tv_string + "\n" + mt_string
    if tracker_weights_info:
        result += "\nMPT Tracker Weights: " + tracker_weights_info
    return result