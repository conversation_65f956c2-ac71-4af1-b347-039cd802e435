from app import app
import dash
from dash import Output, Input, State
import pandas as pd
import json


@app.callback(
    Output('portfolio-allocation', 'value', allow_duplicate=True),
    [Input('efficient-frontier', 'clickData'),
     Input('use-minvar', 'n_clicks'),
     Input('use-maxsharpe', 'n_clicks'),
     Input('use-maxreturn', 'n_clicks'),
     Input('use-maxcf', 'n_clicks'),
     Input('use-invert', 'n_clicks'),
     Input('use-usd', 'n_clicks'),
     Input('use-eur', 'n_clicks'),
     Input('use-gbp', 'n_clicks'),
     Input('use-aud', 'n_clicks'),
     Input('use-nzd', 'n_clicks'),
     Input('use-cad', 'n_clicks'),
     Input('use-chf', 'n_clicks'),
     Input('use-jpy', 'n_clicks')],
    [State('mpt-weights-store', 'data'),
     State('portfolio-allocation', 'value')],
    prevent_initial_call=True
)
def update_allocation(click_frontier, minvar_clicks, maxsharpe_clicks,
                      maxsortino_clicks, maxreturn_clicks,
                      maxcf_clicks, invert_clicks, usd_clicks, eur_clicks, gbp_clicks, aud_clicks,
                      nzd_clicks, cad_clicks, chf_clicks, jpy_clicks, weights_data,
                      current_allocation):
    ctx = dash.callback_context
    if not ctx.triggered:
        raise dash.exceptions.PreventUpdate
    triggered_id = ctx.triggered[0]['prop_id'].split('.')[0]

    # Helper function to normalize weights to absolute sum of 1
    def normalize_abs_weights(weights_dict):
        abs_sum = sum(abs(w) for w in weights_dict.values())
        if abs_sum > 0:
            return {k: v/abs_sum for k, v in weights_dict.items()}
        return weights_dict

    # Currency button handlers
    if triggered_id == "use-usd":
        weights = {"EURUSD": -0.1, "GBPUSD": -0.1, "AUDUSD": -0.1, "NZDUSD": -0.1,
                  "USDCAD": 0.1, "USDCHF": 0.1, "USDJPY": 0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    elif triggered_id == "use-eur":
        weights = {"EURUSD": 0.1, "EURGBP": 0.1, "EURAUD": 0.1, "EURNZD": 0.1,
                  "EURCAD": 0.1, "EURCHF": 0.1, "EURJPY": 0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    elif triggered_id == "use-gbp":
        weights = {"GBPUSD": 0.1, "EURGBP": -0.1, "GBPAUD": 0.1, "GBPNZD": 0.1,
                  "GBPCAD": 0.1, "GBPCHF": 0.1, "GBPJPY": 0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    elif triggered_id == "use-aud":
        weights = {"AUDUSD": 0.1, "EURAUD": -0.1, "GBPAUD": -0.1, "AUDNZD": 0.1,
                  "AUDCAD": 0.1, "AUDCHF": 0.1, "AUDJPY": 0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    elif triggered_id == "use-nzd":
        weights = {"NZDUSD": 0.1, "EURNZD": -0.1, "GBPNZD": -0.1, "AUDNZD": -0.1,
                  "NZDCAD": 0.1, "NZDCHF": 0.1, "NZDJPY": 0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    elif triggered_id == "use-cad":
        weights = {"USDCAD": -0.1, "EURCAD": -0.1, "GBPCAD": -0.1, "AUDCAD": -0.1,
                  "NZDCAD": -0.1, "CADCHF": 0.1, "CADJPY": 0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    elif triggered_id == "use-chf":
        weights = {"USDCHF": -0.1, "EURCHF": -0.1, "GBPCHF": -0.1, "AUDCHF": -0.1,
                  "NZDCHF": -0.1, "CADCHF": -0.1, "CHFJPY": 0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    elif triggered_id == "use-jpy":
        weights = {"USDJPY": -0.1, "EURJPY": -0.1, "GBPJPY": -0.1, "AUDJPY": -0.1,
                  "NZDJPY": -0.1, "CADJPY": -0.1, "CHFJPY": -0.1}
        weights = normalize_abs_weights(weights)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights.items()])
    
    # If Efficient Frontier was clicked, use its candidate's allocation.
    if triggered_id == "efficient-frontier":
        if not click_frontier or not click_frontier.get('points'):
            raise dash.exceptions.PreventUpdate
        # Extract customdata which may be an array or JSON string
        customdata = click_frontier['points'][0].get('customdata')
        if not customdata:
            raise dash.exceptions.PreventUpdate
        try:
            # Handle new array format where JSON is at index 11
            if isinstance(customdata, list) and len(customdata) > 11:
                customdata_str = customdata[11]  # JSON tooltip is at index 11
            else:
                customdata_str = customdata  # Fallback for old format
            candidate = json.loads(customdata_str)
        except Exception as e:
            print("Error parsing Efficient Frontier candidate:", e)
            raise dash.exceptions.PreventUpdate
        # Build allocation string from candidate's combo and weights.
        weights_dict = {sym: weight for sym, weight in zip(candidate.get("combo", []), candidate.get("weights", []))}
        weights_dict = normalize_abs_weights(weights_dict)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights_dict.items()])
    
    # Invert allocation if "use-invert" pressed.
    if triggered_id == "use-invert":
        if not current_allocation:
            raise dash.exceptions.PreventUpdate
        
        # Parse current allocation into a dictionary
        weights_dict = {}
        for part in current_allocation.split(','):
            part = part.strip()
            if not part or ':' not in part:
                continue
                
            symbol, weight_str = part.split(':')
            symbol = symbol.strip()
            locked = False
            
            if symbol.endswith('.'):
                locked = True
                symbol = symbol[:-1]  # Remove trailing dot
                
            weight = float(weight_str)
            # Invert the weight
            weights_dict[symbol + ('.' if locked else '')] = -weight
            
        # Normalize and return
        weights_dict = normalize_abs_weights(weights_dict)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights_dict.items()])
    
    # For weight button clicks, use the weights stored.
    elif triggered_id in ["use-minvar", "use-maxsharpe", "use-maxreturn", "use-maxcf"]:
        if weights_data is None:
            raise dash.exceptions.PreventUpdate

        source_weights = {}
        # Get the locked symbols list
        locked_symbols = set([sym.upper() for sym in weights_data.get("locked", [])])

        if triggered_id == "use-minvar":
            source_weights = weights_data.get("minvar", {})
        elif triggered_id == "use-maxsharpe":
            source_weights = weights_data.get("maxsharpe", {})
        elif triggered_id == "use-maxreturn":
            source_weights = weights_data.get("maxret", {})
        elif triggered_id == "use-maxcf":
            source_weights = weights_data.get("modsharpe", {})
        
        # Prepare weights with proper locked symbol formatting
        weights_dict = {}
        for sym, weight in source_weights.items():
            # Force symbol to uppercase
            symbol = sym.upper()
            # If symbol is locked, add a trailing dot.
            if symbol in locked_symbols:
                symbol_out = f"{symbol}."
            else:
                symbol_out = symbol
            weights_dict[symbol_out] = weight
        
        # Normalize and return
        weights_dict = normalize_abs_weights(weights_dict)
        return ",".join([f"{sym}:{weight:.4f}" for sym, weight in weights_dict.items()])
    
    else:
        raise dash.exceptions.PreventUpdate